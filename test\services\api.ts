// API Service for BAAM Backend Integration
import { Goal, NewTaskForm, Task, User } from '../types';
import { fetchWithAuth } from './auth';
import { auth } from './firebase';

const API_BASE_URL = 'http://localhost:5001';

// Helper to get the current user's ID
const getCurrentUserId = () => {
  const user = auth.currentUser;
  if (!user) {
    throw new Error('No authenticated user found.');
  }
  return user.uid;
};

// Generic API request function using fetchWithAuth
async function apiRequest<T>(endpoint: string, options: RequestInit = {}): Promise<T> {
  const url = `${API_BASE_URL}${endpoint}`;
  const response = await fetchWithAuth(url, options);

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`);
  }

  // For DELETE requests, the body might be empty
  if (options.method === 'DELETE' || response.status === 204) {
    return {} as T;
  }

  return response.json();
}

// Task API functions
export const taskAPI = {
  // Get all tasks for the current user
  async getTasks(): Promise<Task[]> {
    const uid = getCurrentUserId();
    return apiRequest<Task[]>(`/api/users/${uid}/tasks`);
  },

  // Get a specific task
  async getTask(taskId: string): Promise<Task> {
    return apiRequest<Task>(`/api/tasks/${taskId}`);
  },

  // Create a new task
  async createTask(taskData: NewTaskForm): Promise<{ task_id: string; message: string }> {
    const uid = getCurrentUserId();

    // Base payload for all task types
    const basePayload: any = {
      uid,
      title: taskData.title,
      description: taskData.notes || '',
      task_type: taskData.taskType,
      status: 'planned',
      priority: taskData.priority,
      is_recurring: taskData.isRecurring,
    };

    // Helper function to safely convert to ISO string
    const toISOString = (date: Date | string | undefined): string | undefined => {
      if (!date) return undefined;
      if (date instanceof Date) return date.toISOString();
      if (typeof date === 'string') return new Date(date).toISOString();
      return undefined;
    };

    // Add fields based on task type
    switch (taskData.taskType) {
      case 'item':
        if (taskData.dueDate) {
          basePayload.due_date = toISOString(taskData.dueDate);
        }
        break;
      case 'task':
        if (taskData.completeBy) {
          basePayload.due_date = toISOString(taskData.completeBy);
        }
        basePayload.estimated_duration = taskData.duration || 60;
        basePayload.flexibility = taskData.flexibility || 5;
        break;
      case 'event':
        if (taskData.startTime) {
          basePayload.start_time = toISOString(taskData.startTime);
        }
        if (taskData.endTime) {
          basePayload.end_time = toISOString(taskData.endTime);
        }
        break;
    }

    return apiRequest<{ task_id: string; message: string }>('/api/tasks', {
      method: 'POST',
      body: JSON.stringify(basePayload),
    });
  },

  // Update a task
  async updateTask(taskId: string, updates: Partial<Task>): Promise<{ message: string }> {
    const backendUpdates: Record<string, any> = {};
    if (updates.title !== undefined) backendUpdates.title = updates.title;
    // Prefer description field; some callers might still use `notes`
    if ((updates as any).notes !== undefined) backendUpdates.description = (updates as any).notes;
    if (updates.description !== undefined) backendUpdates.description = updates.description;
    // Pass through status if provided; fallback to completed boolean for backward compatibility
    if (updates.status !== undefined) backendUpdates.status = updates.status;
    else if ((updates as any).completed !== undefined) backendUpdates.status = (updates as any).completed ? 'completed' : 'planned';

    return apiRequest<{ message: string }>(`/api/tasks/${taskId}`, {
      method: 'PUT',
      body: JSON.stringify(backendUpdates),
    });
  },

  // Delete a task
  async deleteTask(taskId: string): Promise<{ message: string }> {
    return apiRequest<{ message: string }>(`/api/tasks/${taskId}`, {
      method: 'DELETE',
    });
  },

  // Schedule a task using AI
  async scheduleTask(taskId: string, preferredTime?: string): Promise<any> {
    const scheduleData = preferredTime ? { preferred_time: preferredTime } : {};
    
    return apiRequest(`/api/tasks/${taskId}/schedule`, {
      method: 'POST',
      body: JSON.stringify(scheduleData),
    });
  },
};

// Goal API functions
export const goalAPI = {
  // Get all goals for the current user
  async getGoals(): Promise<Goal[]> {
    const uid = getCurrentUserId();
    return apiRequest<Goal[]>(`/api/users/${uid}/goals`);
  },

  // Create a new goal
  async createGoal(goalData: Partial<Goal>): Promise<{ goal_id: string; message: string }> {
    const uid = getCurrentUserId();
    const backendGoal = {
      uid,
      title: goalData.title,
      description: goalData.description || '',
      status: 'active',
      target_date: goalData.target_date,
      categories: goalData.categories || ['user-created'],
    };

    return apiRequest<{ goal_id: string; message: string }>('/api/goals', {
      method: 'POST',
      body: JSON.stringify(backendGoal),
    });
  },
};

// User API functions
export const userAPI = {
  // Get current user
  async getUser(): Promise<User> {
    const uid = getCurrentUserId();
    return apiRequest<User>(`/api/users/${uid}`);
  },

  // Get user's daily schedule
  async getDailySchedule(date?: string): Promise<any> {
    const uid = getCurrentUserId();
    const dateParam = date || new Date().toISOString().split('T')[0];
    return apiRequest(`/api/users/${uid}/schedule/daily?date=${dateParam}`);
  },

  // Get productivity insights
  async getInsights(): Promise<any> {
    const uid = getCurrentUserId();
    return apiRequest(`/api/users/${uid}/insights`);
  },
};

// Handle Availability API
export const handleAPI = {
  async checkAvailability(handle: string): Promise<{ is_taken: boolean }> {
    if (!handle) {
      return { is_taken: true }; // Or some other default to prevent empty handles
    }
    const url = `${API_BASE_URL}/api/handles/${handle}`;
    const response = await fetch(url); // No auth needed for this endpoint
    if (!response.ok) {
      throw new Error('Failed to check handle availability.');
    }
    return response.json();
  },
};

// AI Chat API
export const chatAPI = {
  async sendMessage(message: string, sessionId?: string): Promise<any> {
    return apiRequest('/api/chat', {
      method: 'POST',
      body: JSON.stringify({
        message,
        session_id: sessionId || 'default',
      }),
    });
  },
};

// Utility functions
function calculateDuration(startTime: string, endTime: string): number {
  const [startHour, startMin] = startTime.split(':').map(Number);
  const [endHour, endMin] = endTime.split(':').map(Number);
  
  const startMinutes = startHour * 60 + startMin;
  const endMinutes = endHour * 60 + endMin;
  
  return Math.max(endMinutes - startMinutes, 15); // Minimum 15 minutes
}

function formatScheduledTime(time: string): string {
  const today = new Date().toISOString().split('T')[0];
  return `${today}T${time}:00`;
}

// Export all APIs
export const api = {
  tasks: taskAPI,
  goals: goalAPI,
  user: userAPI,
  chat: chatAPI,
  handle: handleAPI,
};
